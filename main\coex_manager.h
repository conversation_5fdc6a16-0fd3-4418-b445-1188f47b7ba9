/*
 * WiFi and BLE Coexistence Manager
 * 
 * This module provides dynamic power management and load balancing
 * between WiFi AP and BLE operations for optimal coexistence.
 */

#ifndef COEX_MANAGER_H
#define COEX_MANAGER_H

#include "esp_err.h"
#include "esp_wifi.h"
#include "esp_bt.h"

#ifdef __cplusplus
extern "C" {
#endif

// 共存管理器配置
typedef struct {
    uint8_t wifi_priority;      // WiFi优先级 (0-100)
    uint8_t ble_priority;       // BLE优先级 (0-100)
    uint32_t monitor_interval;  // 监控间隔 (ms)
    bool auto_adjust;           // 是否启用自动调整
} coex_config_t;

// 共存状态信息
typedef struct {
    uint32_t wifi_tx_packets;   // WiFi发送包数
    uint32_t wifi_rx_packets;   // WiFi接收包数
    uint32_t ble_connections;   // BLE连接数
    uint32_t ble_scan_active;   // BLE扫描活跃状态
    int8_t wifi_rssi;          // WiFi信号强度
    int8_t ble_rssi;           // BLE信号强度
    uint8_t wifi_power;        // 当前WiFi功率
    uint8_t ble_power;         // 当前BLE功率
} coex_status_t;

/**
 * @brief 初始化共存管理器
 * 
 * @param config 共存配置参数
 * @return esp_err_t 
 */
esp_err_t coex_manager_init(const coex_config_t *config);

/**
 * @brief 启动共存管理器
 * 
 * @return esp_err_t 
 */
esp_err_t coex_manager_start(void);

/**
 * @brief 停止共存管理器
 * 
 * @return esp_err_t 
 */
esp_err_t coex_manager_stop(void);

/**
 * @brief 获取当前共存状态
 * 
 * @param status 状态信息输出
 * @return esp_err_t 
 */
esp_err_t coex_manager_get_status(coex_status_t *status);

/**
 * @brief 手动调整WiFi和BLE优先级
 * 
 * @param wifi_priority WiFi优先级 (0-100)
 * @param ble_priority BLE优先级 (0-100)
 * @return esp_err_t 
 */
esp_err_t coex_manager_set_priority(uint8_t wifi_priority, uint8_t ble_priority);

/**
 * @brief 通知WiFi活动状态变化
 * 
 * @param active WiFi是否活跃
 * @param client_count 连接的客户端数量
 * @return esp_err_t 
 */
esp_err_t coex_manager_notify_wifi_activity(bool active, uint8_t client_count);

/**
 * @brief 通知BLE活动状态变化
 * 
 * @param connections BLE连接数
 * @param scanning 是否正在扫描
 * @return esp_err_t 
 */
esp_err_t coex_manager_notify_ble_activity(uint8_t connections, bool scanning);

#ifdef __cplusplus
}
#endif

#endif // COEX_MANAGER_H
