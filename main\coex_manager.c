/*
 * WiFi and BLE Coexistence Manager Implementation
 */

#include "coex_manager.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_wifi.h"
#include "esp_bt.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

static const char *TAG = "coex_manager";

// 共存管理器状态
static struct {
    coex_config_t config;
    coex_status_t status;
    esp_timer_handle_t monitor_timer;
    SemaphoreHandle_t mutex;
    bool initialized;
    bool running;
} s_coex_mgr = {0};

// 前向声明
static void coex_monitor_timer_cb(void *arg);
static esp_err_t adjust_power_levels(void);
static esp_err_t update_status(void);

esp_err_t coex_manager_init(const coex_config_t *config)
{
    if (s_coex_mgr.initialized) {
        ESP_LOGW(TAG, "Coexistence manager already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid configuration");
        return ESP_ERR_INVALID_ARG;
    }

    // 复制配置
    memcpy(&s_coex_mgr.config, config, sizeof(coex_config_t));

    // 创建互斥锁
    s_coex_mgr.mutex = xSemaphoreCreateMutex();
    if (!s_coex_mgr.mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return ESP_ERR_NO_MEM;
    }

    // 创建监控定时器
    esp_timer_create_args_t timer_args = {
        .callback = coex_monitor_timer_cb,
        .arg = NULL,
        .name = "coex_monitor"
    };

    esp_err_t ret = esp_timer_create(&timer_args, &s_coex_mgr.monitor_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create monitor timer: %s", esp_err_to_name(ret));
        vSemaphoreDelete(s_coex_mgr.mutex);
        return ret;
    }

    // 初始化状态
    memset(&s_coex_mgr.status, 0, sizeof(coex_status_t));
    s_coex_mgr.status.wifi_power = 15;  // 默认WiFi功率
    s_coex_mgr.status.ble_power = 9;    // 默认BLE功率

    s_coex_mgr.initialized = true;
    ESP_LOGI(TAG, "Coexistence manager initialized");

    return ESP_OK;
}

esp_err_t coex_manager_start(void)
{
    if (!s_coex_mgr.initialized) {
        ESP_LOGE(TAG, "Manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (s_coex_mgr.running) {
        ESP_LOGW(TAG, "Manager already running");
        return ESP_OK;
    }

    // 启动监控定时器
    esp_err_t ret = esp_timer_start_periodic(s_coex_mgr.monitor_timer, 
                                           s_coex_mgr.config.monitor_interval * 1000);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start monitor timer: %s", esp_err_to_name(ret));
        return ret;
    }

    s_coex_mgr.running = true;
    ESP_LOGI(TAG, "Coexistence manager started");

    return ESP_OK;
}

esp_err_t coex_manager_stop(void)
{
    if (!s_coex_mgr.running) {
        return ESP_OK;
    }

    esp_timer_stop(s_coex_mgr.monitor_timer);
    s_coex_mgr.running = false;
    
    ESP_LOGI(TAG, "Coexistence manager stopped");
    return ESP_OK;
}

esp_err_t coex_manager_get_status(coex_status_t *status)
{
    if (!status) {
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(s_coex_mgr.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        memcpy(status, &s_coex_mgr.status, sizeof(coex_status_t));
        xSemaphoreGive(s_coex_mgr.mutex);
        return ESP_OK;
    }

    return ESP_ERR_TIMEOUT;
}

esp_err_t coex_manager_set_priority(uint8_t wifi_priority, uint8_t ble_priority)
{
    if (wifi_priority > 100 || ble_priority > 100) {
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(s_coex_mgr.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        s_coex_mgr.config.wifi_priority = wifi_priority;
        s_coex_mgr.config.ble_priority = ble_priority;
        
        // 立即调整功率
        adjust_power_levels();
        
        xSemaphoreGive(s_coex_mgr.mutex);
        
        ESP_LOGI(TAG, "Priority updated: WiFi=%d%%, BLE=%d%%", wifi_priority, ble_priority);
        return ESP_OK;
    }

    return ESP_ERR_TIMEOUT;
}

esp_err_t coex_manager_notify_wifi_activity(bool active, uint8_t client_count)
{
    if (xSemaphoreTake(s_coex_mgr.mutex, pdMS_TO_TICKS(50)) == pdTRUE) {
        // 更新WiFi活动状态
        if (active) {
            s_coex_mgr.status.wifi_tx_packets++;
        }
        
        xSemaphoreGive(s_coex_mgr.mutex);
    }

    return ESP_OK;
}

esp_err_t coex_manager_notify_ble_activity(uint8_t connections, bool scanning)
{
    if (xSemaphoreTake(s_coex_mgr.mutex, pdMS_TO_TICKS(50)) == pdTRUE) {
        s_coex_mgr.status.ble_connections = connections;
        s_coex_mgr.status.ble_scan_active = scanning ? 1 : 0;
        
        xSemaphoreGive(s_coex_mgr.mutex);
    }

    return ESP_OK;
}

static void coex_monitor_timer_cb(void *arg)
{
    if (!s_coex_mgr.running) {
        return;
    }

    // 更新状态信息
    update_status();

    // 如果启用自动调整，则调整功率
    if (s_coex_mgr.config.auto_adjust) {
        adjust_power_levels();
    }
}

static esp_err_t update_status(void)
{
    if (xSemaphoreTake(s_coex_mgr.mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        return ESP_ERR_TIMEOUT;
    }

    // 这里可以添加更多状态更新逻辑
    // 例如：获取WiFi统计信息、BLE连接状态等

    xSemaphoreGive(s_coex_mgr.mutex);
    return ESP_OK;
}

static esp_err_t adjust_power_levels(void)
{
    // 根据优先级和活动状态调整功率
    uint8_t wifi_power = (s_coex_mgr.config.wifi_priority * 20) / 100;  // 0-20dBm
    uint8_t ble_power = (s_coex_mgr.config.ble_priority * 20) / 100;    // 0-20dBm

    // 确保功率在合理范围内
    wifi_power = (wifi_power < 8) ? 8 : ((wifi_power > 20) ? 20 : wifi_power);
    ble_power = (ble_power < 0) ? 0 : ((ble_power > 20) ? 20 : ble_power);

    // 设置WiFi功率
    esp_err_t ret = esp_wifi_set_max_tx_power(wifi_power * 4);  // WiFi API使用0.25dBm单位
    if (ret == ESP_OK) {
        s_coex_mgr.status.wifi_power = wifi_power;
    }

    // 设置BLE功率
    esp_power_level_t ble_power_level = ESP_PWR_LVL_P9;  // 默认9dBm
    if (ble_power <= 3) ble_power_level = ESP_PWR_LVL_N0;
    else if (ble_power <= 6) ble_power_level = ESP_PWR_LVL_P3;
    else if (ble_power <= 9) ble_power_level = ESP_PWR_LVL_P6;
    else if (ble_power <= 12) ble_power_level = ESP_PWR_LVL_P9;
    else ble_power_level = ESP_PWR_LVL_P12;

    ret = esp_ble_tx_power_set(ESP_BLE_PWR_TYPE_ADV, ble_power_level);
    if (ret == ESP_OK) {
        esp_ble_tx_power_set(ESP_BLE_PWR_TYPE_SCAN, ble_power_level);
        esp_ble_tx_power_set(ESP_BLE_PWR_TYPE_CONN_HDL0, ble_power_level);
        s_coex_mgr.status.ble_power = ble_power;
    }

    return ESP_OK;
}
